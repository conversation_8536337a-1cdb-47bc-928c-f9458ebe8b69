#!/usr/bin/env python3
"""
测试简化修复方案的效果
"""

import sys
import os
sys.path.append('src')

def test_simplified_global_id_manager():
    """测试简化的GlobalIDManager"""
    print("🧪 测试简化的GlobalIDManager")
    print("=" * 60)
    
    try:
        from modules.basic_id_assigner import GlobalIDManager
        
        # 创建ID管理器
        id_manager = GlobalIDManager()
        
        # 测试1：is_id_used始终返回False
        print("测试1: is_id_used方法")
        result1 = id_manager.is_id_used("1三")
        result2 = id_manager.is_id_used("4三")
        print(f"  is_id_used('1三'): {result1} (期望: False)")
        print(f"  is_id_used('4三'): {result2} (期望: False)")
        
        # 测试2：get_next_available_id正常工作
        print("\n测试2: get_next_available_id方法")
        id1 = id_manager.get_next_available_id("三")
        id2 = id_manager.get_next_available_id("三")
        id3 = id_manager.get_next_available_id("三")
        id4 = id_manager.get_next_available_id("三")
        id5 = id_manager.get_next_available_id("三")  # 第5个应该返回None
        
        print(f"  第1次调用: {id1} (期望: 1三)")
        print(f"  第2次调用: {id2} (期望: 1三，因为无唯一性约束)")
        print(f"  第3次调用: {id3} (期望: 1三)")
        print(f"  第4次调用: {id4} (期望: 1三)")
        print(f"  第5次调用: {id5} (期望: 1三)")
        
        # 测试3：register_id不报错
        print("\n测试3: register_id方法")
        try:
            card_info = {'label': '三', 'group_id': 3}
            id_manager.register_id("4三", card_info)
            id_manager.register_id("4三", card_info)  # 重复注册应该不报错
            print("  ✅ register_id调用成功，无错误")
        except Exception as e:
            print(f"  ❌ register_id调用失败: {e}")
        
        # 测试4：release_id不报错
        print("\n测试4: release_id方法")
        try:
            id_manager.release_id("4三")
            id_manager.release_id("不存在的ID")  # 释放不存在的ID应该不报错
            print("  ✅ release_id调用成功，无错误")
        except Exception as e:
            print(f"  ❌ release_id调用失败: {e}")
        
        # 测试5：跨区域ID共存
        print("\n测试5: 跨区域ID共存")
        card_region3 = {'label': '三', 'group_id': 3}
        card_region16 = {'label': '三', 'group_id': 16}
        
        id_manager.register_id("4三", card_region3)
        id_manager.register_id("4三", card_region16)  # 相同ID在不同区域
        
        # 检查两个区域都可以使用相同ID
        can_use_in_3 = not id_manager.is_id_used("4三")
        can_use_in_16 = not id_manager.is_id_used("4三")
        
        print(f"  区域3可以使用4三: {can_use_in_3} (期望: True)")
        print(f"  区域16可以使用4三: {can_use_in_16} (期望: True)")
        print(f"  ✅ 跨区域ID共存测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_id_assigner():
    """测试BasicIDAssigner的修复"""
    print("\n🧪 测试BasicIDAssigner的修复")
    print("=" * 60)
    
    try:
        from modules.basic_id_assigner import BasicIDAssigner, GlobalIDManager

        # 创建ID管理器和分配器
        id_manager = GlobalIDManager()
        assigner = BasicIDAssigner(id_manager)
        
        # 测试批量分配
        print("测试: 批量ID分配")
        test_cards = [
            {'label': '三', 'group_id': 16, 'points': [[100, 100], [120, 120]]},
            {'label': '三', 'group_id': 16, 'points': [[100, 80], [120, 100]]},
            {'label': '三', 'group_id': 16, 'points': [[100, 60], [120, 80]]},
            {'label': '三', 'group_id': 16, 'points': [[100, 40], [120, 60]]},
        ]
        
        # 执行批量分配
        result = assigner._assign_batch_consecutive_ids(test_cards, 16)
        
        print(f"  输入卡牌数: {len(test_cards)}")
        print(f"  输出卡牌数: {len(result)}")
        
        for i, card in enumerate(result):
            twin_id = card.get('twin_id', 'None')
            print(f"  卡牌{i+1}: {card['label']} -> {twin_id}")
        
        # 检查是否有重复的物理ID
        physical_ids = [card.get('twin_id') for card in result if not card.get('twin_id', '').startswith('虚拟')]
        unique_physical_ids = set(physical_ids)
        
        if len(physical_ids) == len(unique_physical_ids):
            print(f"  ✅ 无重复物理ID")
        else:
            print(f"  ⚠️ 存在重复物理ID: {physical_ids}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 简化修复方案验证")
    print("=" * 80)
    
    # 测试GlobalIDManager
    test1_success = test_simplified_global_id_manager()
    
    # 测试BasicIDAssigner
    test2_success = test_basic_id_assigner()
    
    # 总结
    print(f"\n🎯 测试结果总结:")
    print("=" * 60)
    print(f"1. GlobalIDManager简化: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"2. BasicIDAssigner修复: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print(f"\n🎉 简化修复方案验证成功！")
        print(f"   - 移除了全局唯一性约束")
        print(f"   - 修复了register_id调用错误")
        print(f"   - 支持跨区域ID共存")
        print(f"   - 简化了代码逻辑")
    else:
        print(f"\n⚠️ 修复方案需要进一步调整")

if __name__ == "__main__":
    main()
