#!/usr/bin/env python3
"""
分析本次修复失败的原因
"""

import json
from pathlib import Path
from collections import defaultdict

def load_frame_data(frame_number: int) -> dict:
    """加载指定帧的数据"""
    frame_file = Path(f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_number:05d}.json")
    with open(frame_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def analyze_frame_229_actual_result():
    """分析Frame_00229的实际结果"""
    print("=" * 80)
    print("🔍 Frame_00229 实际修复结果分析")
    print("=" * 80)
    
    frame_229 = load_frame_data(229)
    
    # 分析所有"三"相关的卡牌
    san_cards = []
    for shape in frame_229.get('shapes', []):
        label = shape.get('label', '')
        twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
        
        if '三' in label or '三' in twin_id:
            card_info = {
                'region': shape.get('group_id'),
                'label': label,
                'twin_id': twin_id,
                'is_virtual': shape.get('attributes', {}).get('is_virtual', False),
                'position': shape.get('points', [])
            }
            san_cards.append(card_info)
    
    print(f"\n📊 Frame_00229中所有'三'相关卡牌: {len(san_cards)}张")
    
    # 按区域分组
    by_region = defaultdict(list)
    for card in san_cards:
        by_region[card['region']].append(card)
    
    for region, cards in by_region.items():
        region_name = get_region_name(region)
        print(f"\n  区域{region}（{region_name}）: {len(cards)}张")
        
        # 按Y坐标排序（从下到上）
        if cards:
            cards.sort(key=lambda x: -(x['position'][0][1] + x['position'][2][1]) / 2)
            
        for i, card in enumerate(cards):
            virtual_info = "虚拟" if card['is_virtual'] else "物理"
            print(f"    {i+1}. 标签'{card['label']}' → ID'{card['twin_id']}' ({virtual_info})")
    
    # 检查设计期望
    print(f"\n❗ 设计期望vs实际结果对比:")
    print(f"  设计期望: 区域16从下到上显示 1三 → 2三 → 3三 → 4三")
    
    region_16_cards = by_region.get(16, [])
    if region_16_cards:
        actual_sequence = [card['twin_id'] for card in region_16_cards]
        print(f"  实际结果: 区域16从下到上显示 {' → '.join(actual_sequence)}")
        
        # 检查是否有4三
        has_4_san = any(card['twin_id'] == '4三' for card in region_16_cards)
        has_virtual = any(card['is_virtual'] for card in region_16_cards)
        
        print(f"  关键检查:")
        print(f"    - 是否有'4三': {'✅ 是' if has_4_san else '❌ 否'}")
        print(f"    - 是否有虚拟ID: {'❌ 是' if has_virtual else '✅ 否'}")
        print(f"    - 序列是否正确: {'✅ 是' if actual_sequence == ['1三', '2三', '3三', '4三'] else '❌ 否'}")
    else:
        print(f"  实际结果: 区域16没有'三'卡牌")

def get_region_name(region_id: int) -> str:
    """获取区域名称"""
    region_names = {
        1: "手牌_观战方", 2: "调整_观战方", 3: "抓牌_观战方", 4: "打牌_观战方",
        5: "弃牌_观战方", 6: "吃碰区_观战方", 7: "抓牌_对战方", 8: "打牌_对战方",
        9: "弃牌_对战方", 10: "打出_对战方", 11: "最终弃牌_对战方",
        12: "听牌区_观战方", 13: "听牌区_对战方", 14: "赢方区域_观战方",
        15: "赢方区域_对战方", 16: "吃碰区_对战方", 17: "特殊区域"
    }
    return region_names.get(region_id, f"未知区域{region_id}")

def analyze_fix_failure_reasons():
    """分析修复失败的原因"""
    print("=" * 80)
    print("🔍 修复失败原因分析")
    print("=" * 80)
    
    frame_229 = load_frame_data(229)
    
    # 检查区域3和区域16的ID分布
    region_3_cards = []
    region_16_cards = []
    
    for shape in frame_229.get('shapes', []):
        region = shape.get('group_id')
        twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
        
        if region == 3:
            region_3_cards.append(twin_id)
        elif region == 16:
            region_16_cards.append(twin_id)
    
    print(f"\n📊 ID分布分析:")
    print(f"  区域3的所有ID: {sorted(set(region_3_cards))}")
    print(f"  区域16的所有ID: {sorted(set(region_16_cards))}")
    
    # 检查"三"相关ID
    san_ids_region_3 = [id for id in region_3_cards if '三' in id]
    san_ids_region_16 = [id for id in region_16_cards if '三' in id]
    
    print(f"\n🎯 '三'相关ID分析:")
    print(f"  区域3的'三'ID: {san_ids_region_3}")
    print(f"  区域16的'三'ID: {san_ids_region_16}")
    
    # 分析问题
    print(f"\n❗ 问题分析:")
    
    # 1. 检查是否有4三
    has_4_san_anywhere = any('4三' in id for id in region_3_cards + region_16_cards)
    print(f"  1. 全局是否存在'4三': {'是' if has_4_san_anywhere else '否'}")
    
    # 2. 检查区域16的第4张三卡牌
    if len(san_ids_region_16) >= 4:
        fourth_card_id = san_ids_region_16[3] if len(san_ids_region_16) > 3 else "无"
        print(f"  2. 区域16第4张'三'卡牌ID: {fourth_card_id}")
        print(f"     期望: 4三")
        print(f"     实际: {fourth_card_id}")
        print(f"     匹配: {'✅' if fourth_card_id == '4三' else '❌'}")
    else:
        print(f"  2. 区域16'三'卡牌数量: {len(san_ids_region_16)}张（少于4张）")
    
    # 3. 检查ID重复情况
    all_ids = region_3_cards + region_16_cards
    id_counts = {}
    for id in all_ids:
        if '三' in id:
            id_counts[id] = id_counts.get(id, 0) + 1
    
    duplicates = {id: count for id, count in id_counts.items() if count > 1}
    print(f"  3. '三'相关ID重复情况: {duplicates}")
    
    # 4. 分析修复逻辑是否生效
    print(f"\n🔧 修复逻辑分析:")
    print(f"  修复目标: 允许区域3和区域16的ID共存")
    print(f"  修复结果: 区域3有{len(san_ids_region_3)}个'三'ID，区域16有{len(san_ids_region_16)}个'三'ID")
    
    if '1三' in san_ids_region_3 and '1三' in san_ids_region_16:
        print(f"  ✅ 跨区域ID共存生效: '1三'在区域3和16都存在")
    else:
        print(f"  ❌ 跨区域ID共存未生效")

def provide_next_fix_suggestions():
    """提供下次修复建议"""
    print("=" * 80)
    print("💡 下次修复建议")
    print("=" * 80)
    
    print(f"\n🎯 核心问题:")
    print(f"  当前修复只解决了ID共存问题，但没有解决ID分配逻辑问题")
    print(f"  区域16仍然没有获得期望的'4三'ID")
    
    print(f"\n📋 建议修复方案:")
    print(f"  1. 检查继承机制:")
    print(f"     - 确保区域16能正确继承区域3的'4三'")
    print(f"     - 或者确保区域16能分配到'4三'")
    
    print(f"  2. 修改ID分配优先级:")
    print(f"     - 在跨区域共存场景中，优先分配连续的ID序列")
    print(f"     - 避免重复分配已存在的低序号ID")
    
    print(f"  3. 增强get_next_available_id逻辑:")
    print(f"     - 在允许跨区域共存的情况下，寻找下一个真正可用的ID")
    print(f"     - 而不是重复使用已存在的ID")
    
    print(f"  4. 调试ID分配过程:")
    print(f"     - 添加详细日志，跟踪区域16第4张'三'卡牌的ID分配过程")
    print(f"     - 确认是继承失败还是分配逻辑错误")

def main():
    """主函数"""
    print("🚀 修复失败原因分析脚本")
    
    # 1. 分析实际结果
    analyze_frame_229_actual_result()
    
    # 2. 分析失败原因
    analyze_fix_failure_reasons()
    
    # 3. 提供修复建议
    provide_next_fix_suggestions()
    
    print("\n" + "=" * 80)
    print("✅ 分析完成")

if __name__ == "__main__":
    main()
