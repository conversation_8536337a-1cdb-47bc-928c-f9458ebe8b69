#!/usr/bin/env python3
"""
Frame_00229 区域16 ID分配错误深度验证脚本

验证重点：
1. 对比frame_00060（正确）与frame_00229（错误）的差异
2. 验证"3区域与16区域同一个数字孪生ID会同时存在"的影响
3. 分析GlobalIDManager的状态和行为
4. 追踪跨区域继承机制的执行过程
"""

import json
import sys
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Any, Optional

def load_frame_data(frame_number: int) -> Optional[Dict]:
    """加载指定帧的数据"""
    frame_file = Path(f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_number:05d}.json")
    if not frame_file.exists():
        print(f"❌ 文件不存在: {frame_file}")
        return None
    
    try:
        with open(frame_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return None

def analyze_region_cards(frame_data: Dict, region_id: int) -> List[Dict]:
    """分析指定区域的卡牌"""
    region_cards = []
    for shape in frame_data.get('shapes', []):
        if shape.get('group_id') == region_id:
            card_info = {
                'label': shape.get('label'),
                'twin_id': shape.get('attributes', {}).get('digital_twin_id'),
                'is_virtual': shape.get('attributes', {}).get('is_virtual', False),
                'position': shape.get('points', []),
                'score': shape.get('score')
            }
            region_cards.append(card_info)
    return region_cards

def analyze_id_distribution(frame_data: Dict) -> Dict[str, List[Dict]]:
    """分析ID分布情况"""
    id_distribution = defaultdict(list)
    
    for shape in frame_data.get('shapes', []):
        twin_id = shape.get('attributes', {}).get('digital_twin_id')
        if twin_id:
            card_info = {
                'region': shape.get('group_id'),
                'label': shape.get('label'),
                'is_virtual': shape.get('attributes', {}).get('is_virtual', False),
                'position': shape.get('points', [])
            }
            id_distribution[twin_id].append(card_info)
    
    return dict(id_distribution)

def find_duplicate_ids(id_distribution: Dict[str, List[Dict]]) -> Dict[str, List[Dict]]:
    """找出重复的ID"""
    duplicates = {}
    for twin_id, cards in id_distribution.items():
        if len(cards) > 1:
            duplicates[twin_id] = cards
    return duplicates

def analyze_san_cards(frame_data: Dict) -> Dict[str, Any]:
    """专门分析"三"卡牌的分布"""
    san_cards = []
    for shape in frame_data.get('shapes', []):
        label = shape.get('label', '')
        if '三' in label:
            card_info = {
                'region': shape.get('group_id'),
                'label': label,
                'twin_id': shape.get('attributes', {}).get('digital_twin_id'),
                'is_virtual': shape.get('attributes', {}).get('is_virtual', False),
                'position': shape.get('points', []),
                'y_center': (shape.get('points', [[0,0]])[0][1] + shape.get('points', [[0,0]])[2][1]) / 2
            }
            san_cards.append(card_info)
    
    # 按区域分组
    by_region = defaultdict(list)
    for card in san_cards:
        by_region[card['region']].append(card)
    
    # 对每个区域按Y坐标排序（从下到上）
    for region_cards in by_region.values():
        region_cards.sort(key=lambda x: -x['y_center'])  # 负号表示从下到上
    
    return {
        'all_cards': san_cards,
        'by_region': dict(by_region),
        'total_count': len(san_cards)
    }

def compare_frames(frame_60_data: Dict, frame_229_data: Dict):
    """对比frame_00060和frame_00229"""
    print("=" * 80)
    print("🔍 Frame_00060 vs Frame_00229 对比分析")
    print("=" * 80)
    
    # 分析"三"卡牌分布
    san_60 = analyze_san_cards(frame_60_data)
    san_229 = analyze_san_cards(frame_229_data)
    
    print(f"\n📊 '三'卡牌总数对比:")
    print(f"  Frame_00060: {san_60['total_count']}张")
    print(f"  Frame_00229: {san_229['total_count']}张")
    
    print(f"\n📍 Frame_00060 '三'卡牌分布:")
    for region, cards in san_60['by_region'].items():
        print(f"  区域{region}: {len(cards)}张")
        for i, card in enumerate(cards):
            print(f"    {i+1}. {card['twin_id']} ({'虚拟' if card['is_virtual'] else '物理'})")
    
    print(f"\n📍 Frame_00229 '三'卡牌分布:")
    for region, cards in san_229['by_region'].items():
        print(f"  区域{region}: {len(cards)}张")
        for i, card in enumerate(cards):
            print(f"    {i+1}. {card['twin_id']} ({'虚拟' if card['is_virtual'] else '物理'})")
    
    # 检查ID重复情况
    id_dist_60 = analyze_id_distribution(frame_60_data)
    id_dist_229 = analyze_id_distribution(frame_229_data)
    
    duplicates_60 = find_duplicate_ids(id_dist_60)
    duplicates_229 = find_duplicate_ids(id_dist_229)
    
    print(f"\n🔄 ID重复情况:")
    print(f"  Frame_00060: {len(duplicates_60)}个重复ID")
    for twin_id, cards in duplicates_60.items():
        regions = [card['region'] for card in cards]
        print(f"    {twin_id}: 区域{regions}")
    
    print(f"  Frame_00229: {len(duplicates_229)}个重复ID")
    for twin_id, cards in duplicates_229.items():
        regions = [card['region'] for card in cards]
        print(f"    {twin_id}: 区域{regions}")

def analyze_frame_229_error():
    """深度分析Frame_00229的错误"""
    print("=" * 80)
    print("🔍 Frame_00229 错误深度分析")
    print("=" * 80)
    
    frame_229 = load_frame_data(229)
    if not frame_229:
        return
    
    # 分析区域3和区域16
    region_3_cards = analyze_region_cards(frame_229, 3)
    region_16_cards = analyze_region_cards(frame_229, 16)
    
    print(f"\n📍 区域3（抓牌_观战方）卡牌:")
    for card in region_3_cards:
        print(f"  {card['label']}: {card['twin_id']} ({'虚拟' if card['is_virtual'] else '物理'})")
    
    print(f"\n📍 区域16（吃碰区_对战方）卡牌:")
    for card in region_16_cards:
        print(f"  {card['label']}: {card['twin_id']} ({'虚拟' if card['is_virtual'] else '物理'})")
    
    # 专门分析"三"卡牌
    san_analysis = analyze_san_cards(frame_229)
    
    print(f"\n🎯 '三'卡牌详细分析:")
    print(f"  总计: {san_analysis['total_count']}张")
    
    region_3_san = san_analysis['by_region'].get(3, [])
    region_16_san = san_analysis['by_region'].get(16, [])
    
    print(f"\n  区域3的'三'卡牌: {len(region_3_san)}张")
    for card in region_3_san:
        print(f"    {card['twin_id']} ({'虚拟' if card['is_virtual'] else '物理'})")
    
    print(f"\n  区域16的'三'卡牌: {len(region_16_san)}张 (从下到上)")
    for i, card in enumerate(region_16_san):
        print(f"    {i+1}. {card['twin_id']} ({'虚拟' if card['is_virtual'] else '物理'})")
    
    # 检查关键问题
    print(f"\n❗ 关键问题验证:")
    
    # 1. 检查是否存在4三
    has_4_san = any(card['twin_id'] == '4三' for card in san_analysis['all_cards'])
    print(f"  1. 是否存在'4三'ID: {'是' if has_4_san else '否'}")
    
    if has_4_san:
        san_4_cards = [card for card in san_analysis['all_cards'] if card['twin_id'] == '4三']
        for card in san_4_cards:
            print(f"     '4三'位于区域{card['region']}")
    
    # 2. 检查区域16是否有虚拟三
    has_virtual_san = any(card['twin_id'] == '虚拟三' for card in region_16_san)
    print(f"  2. 区域16是否有'虚拟三': {'是' if has_virtual_san else '否'}")
    
    # 3. 检查ID冲突
    id_distribution = analyze_id_distribution(frame_229)
    duplicates = find_duplicate_ids(id_distribution)
    print(f"  3. 是否存在ID重复: {'是' if duplicates else '否'}")
    
    if duplicates:
        for twin_id, cards in duplicates.items():
            regions = [card['region'] for card in cards]
            print(f"     {twin_id}: 区域{regions}")

def verify_hypothesis():
    """验证假设：3区域与16区域同一个数字孪生ID会同时存在导致的问题"""
    print("=" * 80)
    print("🧪 假设验证：3区域与16区域同一ID同时存在的影响")
    print("=" * 80)
    
    # 加载两个关键帧
    frame_60 = load_frame_data(60)   # 正确的参考帧
    frame_229 = load_frame_data(229) # 错误的问题帧
    
    if not frame_60 or not frame_229:
        print("❌ 无法加载必要的帧数据")
        return
    
    # 分析两帧的ID分布
    id_dist_60 = analyze_id_distribution(frame_60)
    id_dist_229 = analyze_id_distribution(frame_229)
    
    # 查找跨区域ID共存情况
    print("\n🔍 跨区域ID共存分析:")
    
    cross_region_60 = {}
    cross_region_229 = {}
    
    for twin_id, cards in id_dist_60.items():
        regions = set(card['region'] for card in cards)
        if len(regions) > 1:
            cross_region_60[twin_id] = list(regions)
    
    for twin_id, cards in id_dist_229.items():
        regions = set(card['region'] for card in cards)
        if len(regions) > 1:
            cross_region_229[twin_id] = list(regions)
    
    print(f"\nFrame_00060 跨区域ID共存: {len(cross_region_60)}个")
    for twin_id, regions in cross_region_60.items():
        print(f"  {twin_id}: 区域{regions}")
    
    print(f"\nFrame_00229 跨区域ID共存: {len(cross_region_229)}个")
    for twin_id, regions in cross_region_229.items():
        print(f"  {twin_id}: 区域{regions}")
    
    # 特别检查3-16区域对
    print(f"\n🎯 特别关注3-16区域对:")
    
    region_3_16_ids_60 = []
    region_3_16_ids_229 = []
    
    for twin_id, regions in cross_region_60.items():
        if 3 in regions and 16 in regions:
            region_3_16_ids_60.append(twin_id)
    
    for twin_id, regions in cross_region_229.items():
        if 3 in regions and 16 in regions:
            region_3_16_ids_229.append(twin_id)
    
    print(f"  Frame_00060中3-16区域共存ID: {region_3_16_ids_60}")
    print(f"  Frame_00229中3-16区域共存ID: {region_3_16_ids_229}")
    
    # 分析差异
    print(f"\n📊 关键差异分析:")
    print(f"  Frame_00060: 3-16区域共存{len(region_3_16_ids_60)}个ID")
    print(f"  Frame_00229: 3-16区域共存{len(region_3_16_ids_229)}个ID")
    
    if len(region_3_16_ids_229) > len(region_3_16_ids_60):
        print(f"  ⚠️  Frame_00229比Frame_00060多了{len(region_3_16_ids_229) - len(region_3_16_ids_60)}个3-16区域共存ID")
        extra_ids = set(region_3_16_ids_229) - set(region_3_16_ids_60)
        print(f"  额外的ID: {list(extra_ids)}")

def main():
    """主函数"""
    print("🚀 Frame_00229 区域16 ID分配错误验证脚本")
    print("=" * 80)
    
    # 1. 对比分析
    frame_60 = load_frame_data(60)
    frame_229 = load_frame_data(229)
    
    if frame_60 and frame_229:
        compare_frames(frame_60, frame_229)
    
    # 2. 深度分析Frame_00229
    analyze_frame_229_error()
    
    # 3. 验证假设
    verify_hypothesis()
    
    print("\n" + "=" * 80)
    print("✅ 验证完成")

if __name__ == "__main__":
    main()
