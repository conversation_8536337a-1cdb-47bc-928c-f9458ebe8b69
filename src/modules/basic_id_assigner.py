"""
模块2：基础ID分配器 (BasicIDAssigner)
只做一件事：为新卡牌分配基础ID
"""

from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class IDAssignmentResult:
    """ID分配结果"""
    assigned_cards: List[Dict[str, Any]]
    statistics: Dict[str, Any]

class GlobalIDManager:
    """全局ID管理器 - 管理整个牌局的ID状态"""

    def __init__(self):
        # 全局ID注册表：{twin_id: 卡牌信息}
        self.global_id_registry: Dict[str, Dict[str, Any]] = {}

        # 每种牌的ID计数器（全局唯一）
        self.id_counters: Dict[str, int] = {}

        # 有效卡牌标签
        self.valid_labels = [
            "一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
            "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾"
        ]

        # 每种牌的最大数量（80张物理牌设计）
        self.max_cards_per_type = 4

        # 🔧 允许ID共存的特定区域对（最小化修复）
        self.allowed_cross_region_pairs = [(3, 16), (4, 16)]

        logger.info("全局ID管理器初始化完成 - 80张物理牌设计")

    def is_id_used(self, twin_id: str, target_region: int = None) -> bool:
        """检查ID是否已被使用

        Args:
            twin_id: 要检查的ID
            target_region: 目标区域ID，用于跨区域共存检查
        """
        if twin_id not in self.global_id_registry:
            return False

        # 🔧 最小化修复：允许特定区域对的ID共存
        if target_region is not None:
            existing_card = self.global_id_registry[twin_id]
            existing_region = existing_card.get('group_id')

            # 检查是否为允许的跨区域共存对
            if existing_region is not None:
                region_pair = (existing_region, target_region)
                reverse_pair = (target_region, existing_region)
                if region_pair in self.allowed_cross_region_pairs or reverse_pair in self.allowed_cross_region_pairs:
                    logger.debug(f"允许跨区域ID共存: {twin_id} 在区域{existing_region}和{target_region}")
                    return False

        return True

    def register_id(self, twin_id: str, card_info: Dict[str, Any]):
        """注册新的ID"""
        self.global_id_registry[twin_id] = card_info
        logger.debug(f"注册ID: {twin_id}")

    def get_next_available_id(self, label: str, prefer_max_id: bool = False, target_region: int = None) -> Optional[str]:
        """获取下一个可用的物理ID

        Args:
            label: 卡牌标签
            prefer_max_id: 是否优先分配最大序号（偎牌明牌场景）
            target_region: 目标区域ID，用于跨区域共存检查
        """
        if label not in self.id_counters:
            self.id_counters[label] = 0

        if prefer_max_id:
            # 🔧 偎牌明牌场景：优先分配最大可用序号（3号）
            for i in range(self.max_cards_per_type, 0, -1):  # 从4到1倒序检查
                potential_id = f"{i}{label}"
                if not self.is_id_used(potential_id, target_region):
                    return potential_id
        else:
            # 🔧 常规场景：按顺序分配最小可用序号
            for i in range(1, self.max_cards_per_type + 1):
                potential_id = f"{i}{label}"
                if not self.is_id_used(potential_id, target_region):
                    return potential_id

        return None  # 没有可用的物理ID

    def release_id(self, twin_id: str):
        """释放指定的ID，使其可以重新分配"""
        if twin_id in self.global_id_registry:
            del self.global_id_registry[twin_id]
            logger.debug(f"释放ID: {twin_id}")
        else:
            logger.warning(f"尝试释放不存在的ID: {twin_id}")

class BasicIDAssigner:
    """基础ID分配器 - 只为真正的新卡牌分配ID"""

    def __init__(self, global_id_manager: GlobalIDManager):
        self.global_id_manager = global_id_manager

        # 统计信息
        self.assignment_stats = {
            "total_assigned": 0,
            "physical_assigned": 0,
            "virtual_assigned": 0
        }

        logger.info("基础ID分配器初始化完成 - 连接全局ID管理器")
    
    def assign_ids(self, cards: List[Dict[str, Any]]) -> IDAssignmentResult:
        """为真正的新卡牌分配ID（已继承的卡牌不会到这里）"""
        logger.info(f"开始为{len(cards)}张新卡牌分配ID")

        # 🔧 处理需要释放源ID的卡牌（必须在ID分配前执行）
        self._process_id_releases(cards)

        # 🔧 区域6的空间重新分配已在区域流转器中完成，这里不需要重复处理

        # 🔧 按区域分组并应用空间排序
        cards_by_region = self._group_and_sort_cards_by_region(cards)

        assigned_cards = []

        # 按区域处理卡牌
        for region_id, region_cards in cards_by_region.items():
            # 🔧 按标签分组，实现批量连续分配
            cards_by_label = {}
            for card in region_cards:
                # 跳过已有ID的卡牌（这些应该是继承来的）
                if 'twin_id' in card and card['twin_id']:
                    assigned_cards.append(card)
                    logger.debug(f"跳过已有ID的卡牌: {card['twin_id']}")
                    continue

                label = card.get('label', '')
                if label not in cards_by_label:
                    cards_by_label[label] = []
                cards_by_label[label].append(card)

            # 为每个标签的卡牌批量分配连续ID
            for label, label_cards in cards_by_label.items():
                if len(label_cards) == 1:
                    # 单张卡牌，使用原有逻辑
                    assigned_card = self._assign_single_id(label_cards[0])
                    assigned_cards.append(assigned_card)
                else:
                    # 多张相同标签卡牌，批量连续分配
                    batch_assigned = self._assign_batch_consecutive_ids(label_cards, region_id)
                    assigned_cards.extend(batch_assigned)

        # 生成统计信息
        statistics = self._generate_statistics()

        logger.info(f"ID分配完成: 新分配{self.assignment_stats['total_assigned']}张，物理{self.assignment_stats['physical_assigned']}张，虚拟{self.assignment_stats['virtual_assigned']}张")

        return IDAssignmentResult(
            assigned_cards=assigned_cards,
            statistics=statistics
        )



    def _group_and_sort_cards_by_region(self, cards: List[Dict[str, Any]]) -> Dict[int, List[Dict[str, Any]]]:
        """
        按区域分组卡牌并应用空间排序规则

        根据GAME_RULES.md：
        - 吃碰区(6,16)：从下到上分配ID
        - 其他区域：保持原顺序
        """
        cards_by_region = {}

        # 按区域分组
        for card in cards:
            region_id = card.get('group_id', 0)
            if region_id not in cards_by_region:
                cards_by_region[region_id] = []
            cards_by_region[region_id].append(card)

        # 对每个区域应用排序规则
        for region_id, region_cards in cards_by_region.items():
            if region_id in [6, 16]:  # 吃碰区
                # 从下到上排序：按Y坐标从大到小
                cards_by_region[region_id] = self._sort_cards_spatial(region_cards, "bottom_to_top")
                logger.debug(f"吃碰区{region_id}应用从下到上排序: {len(region_cards)}张卡牌")
            # 其他区域保持原顺序

        return cards_by_region

    def _sort_cards_spatial(self, cards: List[Dict[str, Any]], sort_type: str) -> List[Dict[str, Any]]:
        """
        按空间位置排序卡牌

        Args:
            cards: 卡牌列表
            sort_type: 排序类型 ("bottom_to_top" 等)
        """
        def get_y_coordinate(card):
            if 'bbox' in card and len(card['bbox']) >= 2:
                return card['bbox'][1]  # bbox[1]是y1坐标
            elif 'points' in card and card['points']:
                # 取points中所有Y坐标的最小值
                return min(point[1] for point in card['points'])
            return 0

        if sort_type == "bottom_to_top":
            # 从下到上：Y坐标从大到小
            return sorted(cards, key=get_y_coordinate, reverse=True)
        else:
            return cards

    def _assign_single_id(self, card: Dict[str, Any]) -> Dict[str, Any]:
        """为单张卡牌分配ID - 使用全局ID管理器"""
        label = card['label']

        # 处理暗牌
        if label == '暗':
            return self._assign_dark_card_id(card)

        # 处理明牌
        return self._assign_bright_card_id(card)
    
    def _assign_bright_card_id(self, card: Dict[str, Any]) -> Dict[str, Any]:
        """为明牌分配ID - 使用全局ID管理器确保ID唯一性"""
        label = card['label']
        target_region = card.get('group_id')

        # 🔧 最小化修复：传递target_region参数
        available_id = self.global_id_manager.get_next_available_id(label, target_region=target_region)

        card_copy = card.copy()

        if available_id:
            # 分配物理ID：格式为 {序号}{牌面}（不包含区域信息）
            card_copy['twin_id'] = available_id
            card_copy['is_virtual'] = False
            card_copy['sequence_number'] = int(available_id[0])  # 提取序号

            # 注册到全局ID管理器
            self.global_id_manager.register_id(available_id, card_copy)

            self.assignment_stats["total_assigned"] += 1
            self.assignment_stats["physical_assigned"] += 1

            logger.info(f"明牌{label}分配物理ID: {available_id} (区域{card.get('group_id', 'unknown')})")
        else:
            # 物理ID已用完，分配虚拟ID：格式为 虚拟{牌面}（不包含区域信息）
            virtual_id = f"虚拟{label}"
            card_copy['twin_id'] = virtual_id
            card_copy['is_virtual'] = True
            card_copy['virtual_reason'] = f"{label}已达{self.global_id_manager.max_cards_per_type}张上限"

            self.assignment_stats["total_assigned"] += 1
            self.assignment_stats["virtual_assigned"] += 1

            logger.warning(f"明牌{label}物理ID已用完，分配虚拟ID: {virtual_id} (区域{card.get('group_id', 'unknown')})")

        return card_copy
    
    def _assign_dark_card_id(self, card: Dict[str, Any]) -> Dict[str, Any]:
        """为暗牌分配临时ID（后续会被暗牌处理器关联）"""
        # 暗牌分配临时ID，后续由暗牌处理器进行关联
        # 格式：临时暗_{唯一标识}（不包含区域信息）

        # 生成临时暗牌ID（使用对象ID确保唯一性）
        temp_id = f"临时暗_{id(card)}"

        card_copy = card.copy()
        card_copy['twin_id'] = temp_id
        card_copy['is_virtual'] = False
        card_copy['is_dark'] = True
        card_copy['needs_association'] = True  # 标记需要关联

        self.assignment_stats["total_assigned"] += 1

        logger.info(f"暗牌分配临时ID: {temp_id} (区域{card.get('group_id', 'unknown')}, 等待关联)")
        return card_copy
    
    def _generate_statistics(self) -> Dict[str, Any]:
        """生成统计信息"""
        return {
            "total_assigned": self.assignment_stats["total_assigned"],
            "physical_assigned": self.assignment_stats["physical_assigned"],
            "virtual_assigned": self.assignment_stats["virtual_assigned"],
            "global_id_count": len(self.global_id_manager.global_id_registry),
            "id_counters": dict(self.global_id_manager.id_counters),
            "max_cards_per_type": self.global_id_manager.max_cards_per_type
        }

    def reset_counters(self):
        """重置计数器（用于新局开始）"""
        self.global_id_manager.global_id_registry.clear()
        self.global_id_manager.id_counters.clear()
        self.assignment_stats = {
            "total_assigned": 0,
            "physical_assigned": 0,
            "virtual_assigned": 0
        }
        logger.info("全局ID状态已重置")

    def get_current_count(self, label: str) -> int:
        """获取指定标签的当前计数"""
        return self.global_id_manager.id_counters.get(label, 0)

    def can_assign_physical_id(self, label: str, target_region: int = None) -> bool:
        """检查是否可以分配物理ID"""
        if label == '暗':
            return True  # 暗牌由暗牌处理器处理
        return self.global_id_manager.get_next_available_id(label, target_region=target_region) is not None

    def _process_id_releases(self, cards: List[Dict[str, Any]]):
        """处理需要释放源ID的卡牌"""
        released_ids = set()  # 避免重复释放同一个ID

        for card in cards:
            if card.get('release_source_id') and card.get('source_card_id'):
                source_id = card['source_card_id']
                if source_id not in released_ids:
                    self.global_id_manager.release_id(source_id)
                    released_ids.add(source_id)
                    logger.info(f"🔄 释放源ID以供重新分配: {source_id} (区域{card.get('transition_source', '未知')}流转)")

    def _assign_batch_consecutive_ids(self, cards: List[Dict[str, Any]], region_id: int) -> List[Dict[str, Any]]:
        """为同一区域相同标签的多张卡牌批量分配连续ID"""
        if not cards:
            return []

        label = cards[0].get('label', '')

        # 🔧 检查是否有流转卡牌需要完全重新分配
        has_transition_cards = any(
            card.get('from_region_4') or
            card.get('from_region_3') or
            card.get('from_region_7') or
            card.get('transition_source') == '4→16' or
            card.get('transition_source') == '3→16' or
            card.get('transition_source') == '7→16'
            for card in cards
        )

        if has_transition_cards:
            logger.info(f"🔧 检测到流转场景，对区域{region_id}的{len(cards)}张'{label}'牌执行完全重新分配")
            return self._assign_complete_reassignment(cards, region_id, label)
        else:
            logger.info(f"🔧 批量连续分配: 区域{region_id}的{len(cards)}张'{label}'牌")

        assigned_cards = []

        for i, card in enumerate(cards):
            # 🔧 最小化修复：传递target_region参数
            next_id = self.global_id_manager.get_next_available_id(label, target_region=region_id)

            if next_id:
                # 分配物理ID
                updated_card = card.copy()
                updated_card['twin_id'] = next_id
                updated_card['digital_twin_id'] = next_id

                # 注册ID（修复缺少参数的问题）
                self.global_id_manager.register_id(next_id, updated_card)

                logger.info(f"明牌{label}分配物理ID: {next_id} (区域{region_id}, 批量第{i+1}张)")
                assigned_cards.append(updated_card)
            else:
                # 分配虚拟ID
                virtual_id = f"虚拟{label}"
                updated_card = card.copy()
                updated_card['twin_id'] = virtual_id
                updated_card['digital_twin_id'] = virtual_id

                logger.info(f"明牌{label}物理ID已用完，分配虚拟ID: {virtual_id} (区域{region_id}, 批量第{i+1}张)")
                assigned_cards.append(updated_card)

        return assigned_cards

    def _assign_complete_reassignment(self, cards: List[Dict[str, Any]], region_id: int, label: str) -> List[Dict[str, Any]]:
        """
        完全重新分配机制（类似Frame 60）
        当检测到流转场景时，对目标区域的同类型卡牌进行完全重新分配
        """
        logger.info(f"🔄 执行完全重新分配: 区域{region_id}的{len(cards)}张'{label}'牌")

        # 按空间位置排序（从下到上）
        sorted_cards = self._sort_cards_spatial(cards, "bottom_to_top")

        # 🔧 查找继承的源ID，确定起始分配ID
        source_id_num = 1  # 默认从1开始
        for card in sorted_cards:
            source_card_id = card.get('source_card_id')
            if source_card_id:
                # 提取源ID的数字部分（如"2六" -> 2）
                try:
                    extracted_num = int(''.join(filter(str.isdigit, source_card_id)))
                    source_id_num = extracted_num
                    logger.info(f"🔧 检测到源ID {source_card_id}，从{source_id_num}开始重新分配")
                    break
                except (ValueError, TypeError):
                    logger.warning(f"⚠️ 无法解析源ID数字: {source_card_id}")

        assigned_cards = []

        # 从源ID开始重新分配
        for i, card in enumerate(sorted_cards):
            # 计算目标ID：从源ID开始递增
            target_id_num = source_id_num + i
            target_id = f"{target_id_num}{label}"

            # 🔧 最小化修复：检查ID是否可用，考虑跨区域共存
            if self.global_id_manager.is_id_used(target_id, target_region=region_id):
                logger.info(f"🔄 释放被占用的ID以供重新分配: {target_id}")
                self.global_id_manager.release_id(target_id)

            # 分配目标ID
            updated_card = card.copy()
            updated_card['twin_id'] = target_id
            updated_card['digital_twin_id'] = target_id

            # 注册ID
            self.global_id_manager.register_id(target_id, updated_card)

            # 记录分配信息
            transition_source = card.get('transition_source', '')
            if transition_source:
                logger.info(f"明牌{label}完全重新分配ID: {target_id} (区域{region_id}, 位置{i+1}, 来源:{transition_source})")
            else:
                logger.info(f"明牌{label}完全重新分配ID: {target_id} (区域{region_id}, 位置{i+1})")

            assigned_cards.append(updated_card)

        end_id_num = source_id_num + len(cards) - 1
        logger.info(f"✅ 完全重新分配完成: 区域{region_id}的{len(cards)}张'{label}'牌，ID范围: {source_id_num}{label}-{end_id_num}{label}")
        return assigned_cards

def create_basic_id_assigner(global_id_manager: GlobalIDManager = None):
    """创建基础ID分配器"""
    if global_id_manager is None:
        global_id_manager = GlobalIDManager()
    return BasicIDAssigner(global_id_manager)
