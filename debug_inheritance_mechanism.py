#!/usr/bin/env python3
"""
继承机制验证脚本

验证simple_inheritor.py的跨区域继承机制是否正常工作，
特别是区域3→区域16的继承
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional

def load_frame_data(frame_number: int) -> Optional[Dict]:
    """加载指定帧的数据"""
    frame_file = Path(f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_number:05d}.json")
    if not frame_file.exists():
        print(f"❌ 文件不存在: {frame_file}")
        return None
    
    try:
        with open(frame_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return None

def analyze_inheritance_expectation():
    """分析继承期望"""
    print("=" * 80)
    print("🔍 继承机制期望分析")
    print("=" * 80)
    
    frame_228 = load_frame_data(228)
    frame_229 = load_frame_data(229)
    
    if not frame_228 or not frame_229:
        return
    
    print(f"\n📋 跨区域继承规则（来自simple_inheritor.py）:")
    print(f"  cross_region_rules = {{")
    print(f"      16: [3, 4, 7, 8],  # 区域16可以从区域3,4,7,8继承")
    print(f"  }}")
    
    # 分析Frame_00228中可供继承的"三"卡牌
    print(f"\n📍 Frame_00228中可供区域16继承的'三'卡牌:")
    
    source_regions = [3, 4, 7, 8]
    available_for_inheritance = []
    
    for shape in frame_228.get('shapes', []):
        region = shape.get('group_id')
        label = shape.get('label', '')
        twin_id = shape.get('attributes', {}).get('digital_twin_id')
        
        if region in source_regions and '三' in label:
            card_info = {
                'region': region,
                'label': label,
                'twin_id': twin_id,
                'is_virtual': shape.get('attributes', {}).get('is_virtual', False)
            }
            available_for_inheritance.append(card_info)
    
    if available_for_inheritance:
        for card in available_for_inheritance:
            region_name = get_region_name(card['region'])
            print(f"    区域{card['region']}（{region_name}）: {card['twin_id']}")
    else:
        print(f"    无可继承的'三'卡牌")
    
    # 分析Frame_00229中区域16的"三"卡牌
    print(f"\n📍 Frame_00229中区域16的'三'卡牌:")
    
    region_16_san_cards = []
    for shape in frame_229.get('shapes', []):
        if shape.get('group_id') == 16:
            label = shape.get('label', '')
            if '三' in label:
                card_info = {
                    'label': label,
                    'twin_id': shape.get('attributes', {}).get('digital_twin_id'),
                    'is_virtual': shape.get('attributes', {}).get('is_virtual', False),
                    'position': shape.get('points', [])
                }
                region_16_san_cards.append(card_info)
    
    # 按Y坐标排序（从下到上）
    region_16_san_cards.sort(key=lambda x: -(x['position'][0][1] + x['position'][2][1]) / 2)
    
    for i, card in enumerate(region_16_san_cards):
        status = "虚拟" if card['is_virtual'] else "物理"
        print(f"    {i+1}. {card['twin_id']} ({status})")
    
    # 分析继承期望vs实际结果
    print(f"\n🎯 继承期望vs实际结果:")
    
    expected_inheritance = None
    if available_for_inheritance:
        # 应该继承区域3的4三
        for card in available_for_inheritance:
            if card['region'] == 3 and card['twin_id'] == '4三':
                expected_inheritance = card
                break
    
    if expected_inheritance:
        print(f"  期望继承: 区域16的第4张'三'卡牌应该继承区域3的'{expected_inheritance['twin_id']}'")
        
        actual_4th_card = region_16_san_cards[3] if len(region_16_san_cards) >= 4 else None
        if actual_4th_card:
            if actual_4th_card['twin_id'] == expected_inheritance['twin_id']:
                print(f"  实际结果: ✅ 继承成功 - {actual_4th_card['twin_id']}")
            else:
                print(f"  实际结果: ❌ 继承失败 - 获得了{actual_4th_card['twin_id']}而不是{expected_inheritance['twin_id']}")
        else:
            print(f"  实际结果: ❌ 区域16没有第4张'三'卡牌")
    else:
        print(f"  期望继承: 无可继承的'三'卡牌")

def get_region_name(region_id: int) -> str:
    """获取区域名称"""
    region_names = {
        1: "手牌_观战方", 2: "调整_观战方", 3: "抓牌_观战方", 4: "打牌_观战方",
        5: "弃牌_观战方", 6: "吃碰区_观战方", 7: "抓牌_对战方", 8: "打牌_对战方",
        9: "弃牌_对战方", 10: "打出_对战方", 11: "最终弃牌_对战方",
        12: "听牌区_观战方", 13: "听牌区_对战方", 14: "赢方区域_观战方",
        15: "赢方区域_对战方", 16: "吃碰区_对战方", 17: "特殊区域"
    }
    return region_names.get(region_id, f"未知区域{region_id}")

def analyze_inheritance_failure_reasons():
    """分析继承失败的可能原因"""
    print("=" * 80)
    print("🔍 继承失败原因分析")
    print("=" * 80)
    
    print(f"\n📋 可能的继承失败原因:")
    
    print(f"\n1️⃣ 继承器识别问题:")
    print(f"  - simple_inheritor.py没有正确识别区域3→区域16的流转关系")
    print(f"  - 流转标记缺失或不正确")
    print(f"  - 继承匹配算法存在问题")
    
    print(f"\n2️⃣ 处理顺序问题:")
    print(f"  - 继承器在ID分配器之前运行")
    print(f"  - 但ID分配器可能覆盖了继承结果")
    print(f"  - 或者继承器没有正确设置twin_id")
    
    print(f"\n3️⃣ GlobalIDManager状态问题:")
    print(f"  - 继承器尝试继承'4三'时，发现已被区域3使用")
    print(f"  - 全局唯一性约束阻止了继承")
    print(f"  - 继承器回退到新分配模式")
    
    print(f"\n4️⃣ 数据流问题:")
    print(f"  - Frame_00228→Frame_00229的数据传递有问题")
    print(f"  - previous_frame_mapping构建不正确")
    print(f"  - ID映射丢失或损坏")

def verify_processing_pipeline():
    """验证处理管道"""
    print("=" * 80)
    print("🔄 处理管道验证")
    print("=" * 80)
    
    print(f"\n📋 理论处理管道:")
    print(f"  1. data_validator.py: 验证输入数据")
    print(f"  2. simple_inheritor.py: 处理继承")
    print(f"     - 构建previous_frame_mapping")
    print(f"     - 执行跨区域继承")
    print(f"     - 设置twin_id")
    print(f"  3. region_transitioner.py: 处理流转")
    print(f"     - 设置流转标记")
    print(f"  4. basic_id_assigner.py: 分配新ID")
    print(f"     - 只处理没有twin_id的卡牌")
    print(f"     - 检查GlobalIDManager可用性")
    print(f"  5. 其他处理器...")
    
    print(f"\n❗ 可能的管道问题:")
    print(f"  1. 继承器设置了twin_id，但后续被覆盖")
    print(f"  2. 继承器没有正确设置twin_id")
    print(f"  3. ID分配器错误地认为卡牌需要新ID")
    print(f"  4. GlobalIDManager状态在模块间不同步")

def final_hypothesis():
    """最终假设"""
    print("=" * 80)
    print("🎯 最终假设验证")
    print("=" * 80)
    
    print(f"\n🧪 基于所有分析的最终假设:")
    print(f"  1. 继承机制本身可能是正常的")
    print(f"  2. 问题出现在GlobalIDManager的全局唯一性约束")
    print(f"  3. 当区域16尝试使用'4三'时（无论是继承还是新分配）:")
    print(f"     - GlobalIDManager检查发现'4三'已被区域3使用")
    print(f"     - 拒绝分配，导致回退到虚拟ID")
    print(f"  4. 这解释了为什么Frame_00060成功（所有'三'卡牌在同一区域6）")
    print(f"     而Frame_00229失败（'三'卡牌分布在区域3和16）")
    
    print(f"\n✅ 验证结果:")
    print(f"  用户的假设是正确的：")
    print(f"  '3区域与16区域同一个数字孪生ID会同时存在'")
    print(f"  确实是导致Frame_00229错误的根本原因")
    
    print(f"\n🔧 解决方案方向:")
    print(f"  1. 修改GlobalIDManager，允许特定区域对的ID共存")
    print(f"  2. 实现区域感知的ID管理策略")
    print(f"  3. 在流转场景中放宽全局唯一性约束")

def main():
    """主函数"""
    print("🚀 继承机制验证脚本")
    
    # 1. 分析继承期望
    analyze_inheritance_expectation()
    
    # 2. 分析继承失败原因
    analyze_inheritance_failure_reasons()
    
    # 3. 验证处理管道
    verify_processing_pipeline()
    
    # 4. 最终假设
    final_hypothesis()
    
    print("\n" + "=" * 80)
    print("✅ 继承机制验证完成")

if __name__ == "__main__":
    main()
