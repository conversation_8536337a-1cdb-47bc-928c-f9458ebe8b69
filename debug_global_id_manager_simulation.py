#!/usr/bin/env python3
"""
GlobalIDManager行为模拟脚本

模拟Frame_00229处理过程中GlobalIDManager的状态变化，
验证"虚拟三"分配的具体原因
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional

class MockGlobalIDManager:
    """模拟GlobalIDManager的行为"""
    
    def __init__(self):
        self.global_id_registry: Dict[str, Dict[str, Any]] = {}
        self.max_cards_per_type = 4
        
    def is_id_used(self, twin_id: str) -> bool:
        """检查ID是否已被使用"""
        return twin_id in self.global_id_registry
    
    def register_id(self, twin_id: str, card_info: Dict[str, Any]):
        """注册新的ID"""
        self.global_id_registry[twin_id] = card_info
        print(f"    📝 注册ID: {twin_id} (区域{card_info.get('group_id', 'unknown')})")
    
    def get_next_available_id(self, label: str, prefer_max_id: bool = False) -> Optional[str]:
        """获取下一个可用的物理ID"""
        print(f"    🔍 查找'{label}'的下一个可用ID...")
        
        if prefer_max_id:
            # 偎牌明牌场景：优先分配最大可用序号
            for i in range(self.max_cards_per_type, 0, -1):
                potential_id = f"{i}{label}"
                is_used = self.is_id_used(potential_id)
                print(f"      检查{potential_id}: {'已使用' if is_used else '可用'}")
                if not is_used:
                    return potential_id
        else:
            # 常规场景：按顺序分配最小可用序号
            for i in range(1, self.max_cards_per_type + 1):
                potential_id = f"{i}{label}"
                is_used = self.is_id_used(potential_id)
                print(f"      检查{potential_id}: {'已使用' if is_used else '可用'}")
                if not is_used:
                    return potential_id
        
        print(f"      ❌ 所有物理ID已用完")
        return None
    
    def show_registry_state(self):
        """显示注册表状态"""
        print(f"\n📊 当前注册表状态:")
        if not self.global_id_registry:
            print(f"    (空)")
        else:
            for twin_id, card_info in self.global_id_registry.items():
                region = card_info.get('group_id', 'unknown')
                print(f"    {twin_id}: 区域{region}")

def load_frame_data(frame_number: int) -> Optional[Dict]:
    """加载指定帧的数据"""
    frame_file = Path(f"output/calibration_gt_final_with_digital_twin/labels/frame_{frame_number:05d}.json")
    if not frame_file.exists():
        print(f"❌ 文件不存在: {frame_file}")
        return None
    
    try:
        with open(frame_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return None

def simulate_frame_229_processing():
    """模拟Frame_00229的处理过程"""
    print("=" * 80)
    print("🧪 Frame_00229 GlobalIDManager行为模拟")
    print("=" * 80)
    
    frame_229 = load_frame_data(229)
    if not frame_229:
        return
    
    # 创建模拟的GlobalIDManager
    mock_manager = MockGlobalIDManager()
    
    # 按处理顺序模拟ID分配
    print(f"\n🔄 模拟ID分配过程:")
    
    # 1. 首先处理所有非区域16的卡牌（模拟继承和其他区域的分配）
    print(f"\n1️⃣ 处理非区域16的卡牌:")
    
    non_region_16_cards = []
    region_16_cards = []
    
    for shape in frame_229.get('shapes', []):
        if shape.get('group_id') == 16:
            region_16_cards.append(shape)
        else:
            non_region_16_cards.append(shape)
    
    # 按区域分组处理非区域16的卡牌
    from collections import defaultdict
    cards_by_region = defaultdict(list)
    for shape in non_region_16_cards:
        region = shape.get('group_id')
        cards_by_region[region].append(shape)
    
    # 模拟处理每个区域
    for region in sorted(cards_by_region.keys()):
        cards = cards_by_region[region]
        print(f"\n  区域{region}: {len(cards)}张卡牌")
        
        for shape in cards:
            twin_id = shape.get('attributes', {}).get('digital_twin_id')
            if twin_id and not twin_id.startswith('虚拟'):
                card_info = {'group_id': region, 'label': shape.get('label')}
                mock_manager.register_id(twin_id, card_info)
    
    mock_manager.show_registry_state()
    
    # 2. 处理区域16的卡牌
    print(f"\n2️⃣ 处理区域16的卡牌:")
    print(f"  区域16: {len(region_16_cards)}张卡牌")
    
    # 按标签分组区域16的卡牌
    region_16_by_label = defaultdict(list)
    for shape in region_16_cards:
        label = shape.get('label', '')
        region_16_by_label[label].append(shape)
    
    # 特别关注"三"卡牌的处理
    san_cards = region_16_by_label.get('三', []) + region_16_by_label.get('1三', []) + \
                region_16_by_label.get('2三', []) + region_16_by_label.get('3三', []) + \
                region_16_by_label.get('虚拟三', [])
    
    # 找出所有"三"相关的卡牌
    all_san_cards = []
    for shape in region_16_cards:
        label = shape.get('label', '')
        if '三' in label:
            all_san_cards.append(shape)
    
    print(f"\n  区域16中的'三'卡牌: {len(all_san_cards)}张")
    
    # 模拟处理每张"三"卡牌
    for i, shape in enumerate(all_san_cards):
        twin_id = shape.get('attributes', {}).get('digital_twin_id')
        label = shape.get('label', '')
        is_virtual = shape.get('attributes', {}).get('is_virtual', False)
        
        print(f"\n    处理第{i+1}张'三'卡牌: {label}")
        
        if twin_id and not is_virtual:
            # 物理ID - 可能是继承得到的
            print(f"      已有物理ID: {twin_id}")
            card_info = {'group_id': 16, 'label': label}
            if not mock_manager.is_id_used(twin_id):
                mock_manager.register_id(twin_id, card_info)
            else:
                print(f"      ⚠️  ID冲突: {twin_id}已被使用")
        
        elif twin_id and is_virtual:
            # 虚拟ID - 分析为什么分配了虚拟ID
            print(f"      虚拟ID: {twin_id}")
            print(f"      🔍 分析虚拟ID分配原因:")
            
            # 模拟get_next_available_id的调用
            available_id = mock_manager.get_next_available_id('三')
            if available_id:
                print(f"      ❓ 奇怪：实际上{available_id}是可用的，为什么分配了虚拟ID？")
            else:
                print(f"      ✅ 确认：所有物理ID已用完，虚拟ID分配合理")
        
        else:
            # 没有ID - 需要分配
            print(f"      需要分配ID")
            available_id = mock_manager.get_next_available_id('三')
            if available_id:
                print(f"      分配物理ID: {available_id}")
                card_info = {'group_id': 16, 'label': label}
                mock_manager.register_id(available_id, card_info)
            else:
                print(f"      分配虚拟ID: 虚拟三")
    
    mock_manager.show_registry_state()

def analyze_inheritance_vs_assignment():
    """分析继承与分配的冲突"""
    print("=" * 80)
    print("🔍 继承与分配冲突分析")
    print("=" * 80)
    
    print(f"\n📋 理论上的处理流程:")
    print(f"  1. simple_inheritor.py: 处理继承")
    print(f"     - 区域16应该从区域3继承'4三'")
    print(f"     - 跨区域继承规则: 16 ← [3, 4, 7, 8]")
    print(f"  2. basic_id_assigner.py: 处理新卡牌ID分配")
    print(f"     - 只为没有twin_id的卡牌分配ID")
    print(f"     - 检查GlobalIDManager的可用性")
    
    print(f"\n❗ 实际问题分析:")
    print(f"  1. 继承阶段:")
    print(f"     - 区域16的第4张'三'卡牌没有成功继承'4三'")
    print(f"     - 可能原因: 继承器没有正确识别流转关系")
    print(f"  2. ID分配阶段:")
    print(f"     - basic_id_assigner发现第4张'三'卡牌没有ID")
    print(f"     - 调用get_next_available_id('三')")
    print(f"     - 发现'4三'已被区域3使用，返回None")
    print(f"     - 分配虚拟ID: '虚拟三'")
    
    print(f"\n🎯 根本原因:")
    print(f"  GlobalIDManager的全局唯一性约束阻止了合理的跨区域ID共存")
    print(f"  - 区域3的'4三'阻止了区域16使用相同ID")
    print(f"  - 即使在流转场景中，这种共存是合理的")

def verify_frame_60_success():
    """验证Frame_00060为什么成功"""
    print("=" * 80)
    print("✅ Frame_00060成功原因验证")
    print("=" * 80)
    
    frame_60 = load_frame_data(60)
    if not frame_60:
        return
    
    # 分析Frame_00060的"三"卡牌分布
    san_cards = []
    for shape in frame_60.get('shapes', []):
        label = shape.get('label', '')
        if '三' in label:
            card_info = {
                'region': shape.get('group_id'),
                'label': label,
                'twin_id': shape.get('attributes', {}).get('digital_twin_id'),
                'is_virtual': shape.get('attributes', {}).get('is_virtual', False)
            }
            san_cards.append(card_info)
    
    print(f"\n📊 Frame_00060的'三'卡牌分布:")
    by_region = {}
    for card in san_cards:
        region = card['region']
        if region not in by_region:
            by_region[region] = []
        by_region[region].append(card)
    
    for region, cards in by_region.items():
        print(f"  区域{region}: {len(cards)}张")
        for card in cards:
            print(f"    {card['twin_id']} ({'虚拟' if card['is_virtual'] else '物理'})")
    
    print(f"\n🔍 成功原因分析:")
    print(f"  1. Frame_00060中所有'三'卡牌都在区域6")
    print(f"  2. 没有跨区域的ID冲突")
    print(f"  3. GlobalIDManager可以正常分配1三-4三")
    print(f"  4. 不存在'3区域与16区域同一个数字孪生ID会同时存在'的情况")

def main():
    """主函数"""
    print("🚀 GlobalIDManager行为模拟脚本")
    
    # 1. 模拟Frame_00229的处理过程
    simulate_frame_229_processing()
    
    # 2. 分析继承与分配的冲突
    analyze_inheritance_vs_assignment()
    
    # 3. 验证Frame_00060的成功原因
    verify_frame_60_success()
    
    print("\n" + "=" * 80)
    print("✅ 模拟分析完成")
    print("\n🎯 结论:")
    print("  Frame_00229的'虚拟三'分配是由于GlobalIDManager的全局唯一性约束")
    print("  阻止了区域16使用已被区域3占用的'4三'ID，即使这种共存在流转场景中是合理的。")

if __name__ == "__main__":
    main()
